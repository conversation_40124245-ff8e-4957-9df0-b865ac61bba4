import { useLiveKitStore, ConnectionState } from "@/hooks/useLiveKitStore";
import { toast } from "sonner";

/**
 * LiveKit Manager Service
 * Handles connection lifecycle and background operations
 */
export class LiveKitManager {
  private static instance: LiveKitManager;
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.startCleanupInterval();
  }

  public static getInstance(): LiveKitManager {
    if (!LiveKitManager.instance) {
      LiveKitManager.instance = new LiveKitManager();
    }
    return LiveKitManager.instance;
  }

  /**
   * Initialize connection for an agent-conversation pair
   */
  public async initializeConnection(
    agentId: string,
    conversationId: string
  ): Promise<void> {
    const store = useLiveKitStore.getState();

    try {
      await store.initializeConnection(agentId, conversationId);
      store.setActiveConnection(agentId, conversationId);
    } catch (error) {
      console.error("LiveKitManager: Failed to initialize connection", error);
      toast.error("Failed to connect to agent");
      throw error;
    }
  }

  /**
   * Switch to a different agent-conversation without disconnecting others
   */
  public switchConnection(agentId: string, conversationId: string): void {
    const store = useLiveKitStore.getState();
    store.setActiveConnection(agentId, conversationId);

    // Initialize connection if it doesn't exist
    const connection = store.getConnection(agentId, conversationId);
    if (
      !connection ||
      connection.connectionState === ConnectionState.DISCONNECTED
    ) {
      this.initializeConnection(agentId, conversationId).catch(console.error);
    }
  }

  /**
   * Get the current active connection
   */
  public getActiveConnection() {
    const store = useLiveKitStore.getState();
    if (!store.activeConnectionKey) return null;

    const [agentId, conversationId] = store.activeConnectionKey.split("-");
    return store.getConnection(agentId, conversationId);
  }

  /**
   * Handle room disconnection
   */
  public handleDisconnection(agentId: string, conversationId: string): void {
    const store = useLiveKitStore.getState();
    store.updateConnection(agentId, conversationId, {
      connectionState: ConnectionState.DISCONNECTED,
    });

    console.warn(
      `LiveKit connection disconnected for agent ${agentId}, conversation ${conversationId}`
    );
    toast.error("Connection to agent lost. Attempting to reconnect...");

    // Attempt to reconnect after a delay
    setTimeout(() => {
      this.initializeConnection(agentId, conversationId).catch(console.error);
    }, 2000);
  }

  /**
   * Start periodic cleanup of inactive connections
   */
  private startCleanupInterval(): void {
    // Clean up every 10 minutes
    this.cleanupInterval = setInterval(() => {
      const store = useLiveKitStore.getState();
      store.cleanupInactiveConnections();
    }, 10 * 60 * 1000);
  }

  /**
   * Stop cleanup interval (for cleanup)
   */
  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * Disconnect all connections (for logout/cleanup)
   */
  public disconnectAll(): void {
    const store = useLiveKitStore.getState();
    Object.keys(store.connections).forEach((connectionKey) => {
      const [agentId, conversationId] = connectionKey.split("-");
      store.disconnectConnection(agentId, conversationId);
    });
  }

  /**
   * Check if a connection exists and is ready
   */
  public isConnectionReady(agentId: string, conversationId: string): boolean {
    const store = useLiveKitStore.getState();
    const connection = store.getConnection(agentId, conversationId);
    return (
      connection?.connectionState === ConnectionState.CONNECTED &&
      !!connection.token
    );
  }

  /**
   * Get connection status for debugging
   */
  public getConnectionStatus(agentId: string, conversationId: string): string {
    const store = useLiveKitStore.getState();
    const connection = store.getConnection(agentId, conversationId);
    if (!connection) return "Not initialized";
    return `${connection.connectionState} - Token: ${
      connection.token ? "Available" : "Missing"
    }`;
  }
}

// Export singleton instance
export const livekitManager = LiveKitManager.getInstance();
