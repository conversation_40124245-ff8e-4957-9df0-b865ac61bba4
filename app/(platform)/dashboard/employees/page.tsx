"use client";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { Users, ArchiveX, LoaderIcon } from "lucide-react";
import { AllEmployeesContent } from "./_components/AllEmployeesContent";
import { EmployeeBenchContent } from "./_components/EmployeeBenchContent";
import { SearchBar } from "@/components/shared/SearchBar";
import { useQuery } from "@tanstack/react-query";
import { agentApi } from "@/app/api/agent";
import { AgentBase } from "@/shared/interfaces";

export default function EmployeesPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all-employees");
  const [benchEmployees, setBenchEmployees] = useState<AgentBase[]>([]);
  const [agents, setAgents] = useState<AgentBase[]>([]);

  const { data: agentsResponse, isLoading: isLoadingAgents } = useQuery({
    queryKey: ["agents"],
    queryFn: () => agentApi.getAgents(),
  });

  const { data: benchEmployeesResponse, isLoading: isLoadingBenchEmployees } =
    useQuery({
      queryKey: ["benched"],
      queryFn: () => agentApi.getAgents(1, 10, true),
    });

  useEffect(() => {
    if (agentsResponse) {
      setAgents(agentsResponse.data);
    }
  }, [agentsResponse]);

  useEffect(() => {
    if (benchEmployeesResponse) {
      setBenchEmployees(benchEmployeesResponse.data);
    }
  }, [benchEmployeesResponse]);

  // Filter employees based on active tab and search query
  const filteredEmployees =
    activeTab === "all-employees"
      ? agents.filter((employee) =>
          employee.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : agents;

  const filteredBenchEmployees =
    activeTab === "employee-bench"
      ? benchEmployees.filter((employee) =>
          (employee as { name: string }).name
            .toLowerCase()
            .includes(searchQuery.toLowerCase())
        )
      : benchEmployees;

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  if (isLoadingAgents || isLoadingBenchEmployees) {
    return (
      <div className="flex flex-col gap-4 items-center justify-center h-screen">
        <LoaderIcon className="w-10 h-10 animate-spin text-brand-primary" />
        <h1 className="text-brand-primary-font text-xl font-semibold text-center">
          Please Wait While we are fetching your agents
        </h1>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col h-full">
      {/* Container for search input and tabs */}
      <div className="bg-brand-card border border-brand-stroke flex flex-col gap-4 p-4 pb-0 sticky top-0 z-20">
        {/* Search input above tabs */}
        <SearchBar onSearch={setSearchQuery} />

        <Tabs
          defaultValue="all-employees"
          className="w-full"
          onValueChange={handleTabChange}
        >
          <TabsList className="w-2/4 justify-start gap-4 bg-transparent px-3">
            <TabsTrigger
              value="all-employees"
              className="flex items-center gap-2 text-brand-primary-font font-medium data-[state=active]:text-brand-primary data-[state=active]:border-b-[1.5px] data-[state=active]:border-brand-primary dark:data-[state=active]:text-brand-primary dark:data-[state=active]:border-b-2 dark:data-[state=active]:border-brand-primary"
            >
              <Users className="w-4 h-4" />
              All employees
            </TabsTrigger>
            <TabsTrigger
              value="employee-bench"
              className="flex items-center gap-2 text-brand-primary-font font-medium data-[state=active]:text-brand-primary data-[state=active]:border-b-[1.5px] data-[state=active]:border-brand-primary dark:data-[state=active]:text-brand-primary dark:data-[state=active]:border-b-2 dark:data-[state=active]:border-brand-primary"
            >
              <ArchiveX className="w-4 h-4" />
              Employee bench
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Tab content with background color */}
      <div className="flex flex-col h-full bg-brand-background rounded-lg p-4">
        <Tabs defaultValue="all-employees" value={activeTab} className="w-full">
          <TabsContent value="all-employees" className="w-full">
            <div className="w-full">
              <AllEmployeesContent employees={filteredEmployees} />
            </div>
          </TabsContent>

          <TabsContent value="employee-bench">
            <div>
              <EmployeeBenchContent
                employees={filteredBenchEmployees}
                searchQuery={searchQuery}
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
