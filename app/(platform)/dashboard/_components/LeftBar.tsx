"use client";

import Image from "next/image";
import { leftBarItems, noTextLogoPath, employees } from "@/shared/constants";
import { ModeToggle } from "@/components/shared/ModeToggle";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { CustomSeparator } from "@/components/shared/CustomSeparator";
import { PanelRightCloseIcon } from "lucide-react";
import { LeftBarProps } from "@/shared/interfaces";
import { dashboardRoute, employeeWindowRoute } from "@/shared/routes";
import { UserAvatar } from "@/components/shared/UserAvatar";
import { NotificationButton } from "@/components/shared/NotificationButton";
export const LeftBar = ({
  isSecondaryBarOpen,
  toggleSecondaryBar,
}: LeftBarProps) => {
  const pathname = usePathname();

  const pathSegments = pathname.split("/").filter((segment) => segment !== "");
  const lastSegment = pathSegments[pathSegments.length - 1];

  return (
    <div className="w-[92px] h-screen flex flex-col justify-between items-center p-4 border-r bg-brand-card sticky top-0 ">
      {!isSecondaryBarOpen && (
        <div
          onClick={toggleSecondaryBar}
          role="button"
          className="absolute top-4 -right-6 bg-brand-background rounded-full p-2 cursor-pointer shadow-xl border border-brand-stroke text-brand-primary-font"
        >
          <PanelRightCloseIcon strokeWidth={1.2} />
        </div>
      )}

      <div className="flex flex-col items-center gap-6">
        <Link href={dashboardRoute}>
          <Image src={noTextLogoPath} alt="Logo" width={32} height={32} />
        </Link>

        <CustomSeparator />

        <nav className="flex flex-col items-center gap-2 py-2">
          {leftBarItems.map((item) => {
            const href = item.href
              ? item.name === "Home"
                ? dashboardRoute
                : item.href
              : "#";
            const isActive =
              (item.name === "Home" &&
                (lastSegment === "dashboard" || !lastSegment)) ||
              (item.name !== "Home" &&
                lastSegment === item.href?.split("/").pop());

            return (
              <Link
                href={href}
                key={item.name}
                className={`flex flex-col items-center justify-center space-y-1 cursor-pointer  transition-all duration-100 ease-in rounded-sm w-16 h-16 ${
                  isActive
                    ? "bg-brand-clicked text-brand-primary"
                    : "hover:bg-brand-card-hover hover:text-brand-primary"
                }`}
              >
                <item.icon size={24} strokeWidth={1.2} />
                <span className="text-xs ">{item.name}</span>
              </Link>
            );
          })}
        </nav>

        <CustomSeparator />
      </div>

      <div className="flex flex-col items-center gap-8 pb-4">
        <NotificationButton />
        <ModeToggle />
        <UserAvatar className="w-[35px] h-[35px]" />
      </div>
    </div>
  );
};
