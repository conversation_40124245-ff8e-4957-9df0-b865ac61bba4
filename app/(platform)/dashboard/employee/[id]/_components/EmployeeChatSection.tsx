"use client";

import React, { useEffect } from "react";
import { Agent } from "@/shared/interfaces";
import { useCallStore } from "@/hooks/useCallStore";
import { LiveKitCallPanel } from "./LiveKitCallPanel";
import { useLiveKitStore, ConnectionState } from "@/hooks/useLiveKitStore";
import { livekitManager } from "@/services/livekitManager";
import { LiveKitRoom } from "@livekit/components-react";
import { ChatInterface } from "./ChatInterface";
import { Skeleton } from "@/components/ui/skeleton";
import { useParams } from "next/navigation";

interface EmployeeChatSectionProps {
  agent: Agent;
}

export const EmployeeChatSection = ({ agent }: EmployeeChatSectionProps) => {
  const { isCallPanelVisible, setIsCallPanelVisible } = useCallStore();
  const params = useParams();
  const conversationId = params.chatId as string;

  // Get connection from global store
  const connection = useLiveKitStore((state) =>
    state.getConnection(agent.id, conversationId)
  );

  useEffect(() => {
    // Initialize connection in background
    livekitManager
      .initializeConnection(agent.id, conversationId)
      .catch((error) => {
        console.error("Failed to initialize connection:", error);
      });

    // Set as active connection
    livekitManager.switchConnection(agent.id, conversationId);

    // Cleanup function
    return () => {
      setIsCallPanelVisible(false); // Reset panel visibility on unmount
    };
  }, [agent.id, conversationId, setIsCallPanelVisible]);

  // Check if we should use cached token or fetch new one
  const shouldUseConnection =
    connection &&
    connection.connectionState === ConnectionState.CONNECTED &&
    connection.token;

  const tokenToUse = shouldUseConnection ? connection.token : null;

  const handleDisconnect = () => {
    console.warn("LiveKit connection disconnected unexpectedly");
    livekitManager.handleDisconnection(agent.id, conversationId);
  };

  const handleHidePanel = () => {
    setIsCallPanelVisible(false);
  };

  // --- Loading State for Token ---
  if (
    !connection ||
    connection.isLoading ||
    connection.connectionState === ConnectionState.CONNECTING
  ) {
    return (
      <div className="flex items-center justify-center h-full w-full">
        <div className="flex flex-col items-center space-y-2">
          <Skeleton className="h-12 w-12 rounded-full" />
          <Skeleton className="h-4 w-[250px]" />
          <Skeleton className="h-4 w-[200px]" />
          <p className="text-sm text-gray-500">Initializing connection...</p>
        </div>
      </div>
    );
  }

  // --- Error State (No Token after loading) ---
  if (
    connection.connectionState === ConnectionState.FAILED ||
    !connection.token
  ) {
    return (
      <div className="flex items-center justify-center h-full w-full overflow-y-auto">
        <p className="text-red-500">
          {connection.error || "Failed to connect. Please refresh the page."}
        </p>
      </div>
    );
  }

  // Use a unique key for LiveKitRoom to force remount when switching agents
  const roomKey = `${agent.id}-${conversationId}`;

  // --- Main Connected View ---
  return (
    <LiveKitRoom
      key={roomKey}
      serverUrl={process.env.NEXT_PUBLIC_LIVEKIT_URL}
      token={tokenToUse || connection.token}
      connect={true}
      video={false}
      audio={false}
      onDisconnected={handleDisconnect}
      className="flex mx-auto h-full w-full relative"
    >
      <div
        className={`flex flex-col h-full ${
          isCallPanelVisible ? "flex-1" : "w-full max-w-6xl mx-auto"
        } `}
      >
        <ChatInterface agent={agent} />
      </div>

      {isCallPanelVisible && (
        <div className="w-[400px] flex-shrink-0 h-full border-l border-gray-200 ">
          <LiveKitCallPanel handleHidePanel={handleHidePanel} />
        </div>
      )}
    </LiveKitRoom>
  );
};
