import { create } from "zustand";
import { ChatMessage, WorkflowStep } from "@/shared/interfaces";
import { WorkflowStatus } from "@/shared/enums";
import { livekitApi } from "@/app/api/livekit";

// Connection states
export enum ConnectionState {
  DISCONNECTED = "disconnected",
  CONNECTING = "connecting",
  CONNECTED = "connected",
  RECONNECTING = "reconnecting",
  FAILED = "failed",
}

// Individual agent connection data
export interface AgentConnection {
  agentId: string;
  conversationId: string;
  token: string | null;
  connectionState: ConnectionState;
  messages: ChatMessage[];
  workflowStage: WorkflowStatus | null;
  steps: WorkflowStep[];
  isLoading: boolean;
  isMessageLoading: boolean;
  inputDisabled: boolean;
  lastActivity: number;
  error: string | null;
  // Track if this connection should be active
  isActive: boolean;
}

// Store interface
interface LiveKitStoreState {
  // Map of "agentId-conversationId" to connection data
  connections: Record<string, AgentConnection>;

  // Currently active connection
  activeConnectionKey: string | null;

  // Actions
  initializeConnection: (
    agentId: string,
    conversationId: string
  ) => Promise<void>;
  setActiveConnection: (agentId: string, conversationId: string) => void;
  getConnection: (
    agentId: string,
    conversationId: string
  ) => AgentConnection | null;
  updateConnection: (
    agentId: string,
    conversationId: string,
    updates: Partial<AgentConnection>
  ) => void;
  addMessage: (
    agentId: string,
    conversationId: string,
    message: ChatMessage
  ) => void;
  setMessages: (
    agentId: string,
    conversationId: string,
    messages: ChatMessage[]
  ) => void;
  updateWorkflowState: (
    agentId: string,
    conversationId: string,
    workflowStage: WorkflowStatus | null,
    steps: WorkflowStep[]
  ) => void;
  disconnectConnection: (agentId: string, conversationId: string) => void;
  cleanupInactiveConnections: () => void;
  getAllConnections: () => Record<string, AgentConnection>;
  getConnectionCount: () => number;
}

// Helper function to create connection key
const getConnectionKey = (agentId: string, conversationId: string): string => {
  return `${agentId}-${conversationId}`;
};

// Helper function to create default connection
const createDefaultConnection = (
  agentId: string,
  conversationId: string
): AgentConnection => ({
  agentId,
  conversationId,
  token: null,
  connectionState: ConnectionState.DISCONNECTED,
  messages: [],
  workflowStage: null,
  steps: [],
  isLoading: true,
  isMessageLoading: false,
  inputDisabled: false,
  lastActivity: Date.now(),
  error: null,
  isActive: false,
});

export const useLiveKitStore = create<LiveKitStoreState>((set, get) => ({
  connections: {},
  activeConnectionKey: null,

  initializeConnection: async (agentId: string, conversationId: string) => {
    const connectionKey = getConnectionKey(agentId, conversationId);
    const state = get();

    // If connection already exists and is connected, just update activity
    const existingConnection = state.connections[connectionKey];
    if (
      existingConnection &&
      existingConnection.connectionState === ConnectionState.CONNECTED
    ) {
      set({
        connections: {
          ...state.connections,
          [connectionKey]: {
            ...existingConnection,
            lastActivity: Date.now(),
          },
        },
      });
      return;
    }

    // Create or update connection to connecting state
    const connection =
      existingConnection || createDefaultConnection(agentId, conversationId);

    set({
      connections: {
        ...state.connections,
        [connectionKey]: {
          ...connection,
          connectionState: ConnectionState.CONNECTING,
          isLoading: true,
          error: null,
          lastActivity: Date.now(),
        },
      },
    });

    try {
      // Fetch token
      const token = await livekitApi.getToken(
        agentId,
        "single",
        conversationId
      );

      // Update with token
      set({
        connections: {
          ...get().connections,
          [connectionKey]: {
            ...get().connections[connectionKey],
            token,
            connectionState: ConnectionState.CONNECTED,
            isLoading: false,
            error: null,
          },
        },
      });
    } catch (error) {
      console.error("Failed to initialize LiveKit connection:", error);
      set({
        connections: {
          ...get().connections,
          [connectionKey]: {
            ...get().connections[connectionKey],
            connectionState: ConnectionState.FAILED,
            isLoading: false,
            error: error instanceof Error ? error.message : "Failed to connect",
          },
        },
      });
    }
  },

  setActiveConnection: (agentId: string, conversationId: string) => {
    const connectionKey = getConnectionKey(agentId, conversationId);
    const state = get();

    // Mark all connections as inactive first
    const updatedConnections = Object.entries(state.connections).reduce(
      (acc, [key, connection]) => {
        acc[key] = { ...connection, isActive: key === connectionKey };
        return acc;
      },
      {} as Record<string, AgentConnection>
    );

    set({
      activeConnectionKey: connectionKey,
      connections: updatedConnections,
    });
  },

  getConnection: (agentId: string, conversationId: string) => {
    const connectionKey = getConnectionKey(agentId, conversationId);
    return get().connections[connectionKey] || null;
  },

  updateConnection: (
    agentId: string,
    conversationId: string,
    updates: Partial<AgentConnection>
  ) => {
    const connectionKey = getConnectionKey(agentId, conversationId);
    const state = get();
    const existingConnection = state.connections[connectionKey];

    if (existingConnection) {
      set({
        connections: {
          ...state.connections,
          [connectionKey]: {
            ...existingConnection,
            ...updates,
            lastActivity: Date.now(),
          },
        },
      });
    }
  },

  addMessage: (
    agentId: string,
    conversationId: string,
    message: ChatMessage
  ) => {
    const connectionKey = getConnectionKey(agentId, conversationId);
    const state = get();
    const connection = state.connections[connectionKey];

    if (connection) {
      set({
        connections: {
          ...state.connections,
          [connectionKey]: {
            ...connection,
            messages: [...connection.messages, message],
            lastActivity: Date.now(),
          },
        },
      });
    }
  },

  setMessages: (
    agentId: string,
    conversationId: string,
    messages: ChatMessage[]
  ) => {
    const connectionKey = getConnectionKey(agentId, conversationId);
    const state = get();
    const connection = state.connections[connectionKey];

    if (connection) {
      set({
        connections: {
          ...state.connections,
          [connectionKey]: {
            ...connection,
            messages,
            lastActivity: Date.now(),
          },
        },
      });
    }
  },

  updateWorkflowState: (
    agentId: string,
    conversationId: string,
    workflowStage: WorkflowStatus | null,
    steps: WorkflowStep[]
  ) => {
    const connectionKey = getConnectionKey(agentId, conversationId);
    const state = get();
    const connection = state.connections[connectionKey];

    if (connection) {
      set({
        connections: {
          ...state.connections,
          [connectionKey]: {
            ...connection,
            workflowStage,
            steps,
            lastActivity: Date.now(),
          },
        },
      });
    }
  },

  disconnectConnection: (agentId: string, conversationId: string) => {
    const connectionKey = getConnectionKey(agentId, conversationId);
    const state = get();
    const connection = state.connections[connectionKey];

    if (connection) {
      set({
        connections: {
          ...state.connections,
          [connectionKey]: {
            ...connection,
            connectionState: ConnectionState.DISCONNECTED,
            token: null,
            isActive: false,
          },
        },
      });
    }
  },

  cleanupInactiveConnections: () => {
    const state = get();
    const now = Date.now();
    const INACTIVE_THRESHOLD = 30 * 60 * 1000; // 30 minutes

    const activeConnections: Record<string, AgentConnection> = {};

    Object.entries(state.connections).forEach(([key, connection]) => {
      if (now - connection.lastActivity < INACTIVE_THRESHOLD) {
        activeConnections[key] = connection;
      } else {
        // Mark connection as inactive for cleanup
        console.log(`Cleaned up inactive connection: ${key}`);
      }
    });

    set({ connections: activeConnections });
  },

  // Get all active connections for debugging
  getAllConnections: () => {
    return get().connections;
  },

  // Get connection count
  getConnectionCount: () => {
    return Object.keys(get().connections).length;
  },
}));
