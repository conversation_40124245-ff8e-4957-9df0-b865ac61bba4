# LiveKit Persistent Connections Implementation

## Overview

This implementation provides persistent LiveKit connections across agents, allowing users to switch between different agents without losing their connection state or conversation history.

## Key Features

✅ **Persistent Connections**: LiveKit connections remain active when switching between agents
✅ **Message Persistence**: Chat messages and conversation state are preserved
✅ **Background Connection Management**: Connections are initialized and maintained in the background
✅ **Automatic Cleanup**: Inactive connections are automatically cleaned up after 30 minutes
✅ **State Synchronization**: Local component state syncs with global store
✅ **Error Handling**: Robust error handling and reconnection logic

## Architecture

### Core Components

1. **`useLiveKitStore.ts`** - Global Zustand store for managing connections
2. **`livekitManager.ts`** - Service layer for connection lifecycle management
3. **`EmployeeChatSection.tsx`** - Updated to use global connection management
4. **`ChatInterface.tsx`** - Enhanced with state synchronization

### Data Flow

```
User navigates to agent → livekitManager.initializeConnection() → 
Global store creates/retrieves connection → Token fetched in background →
Connection established → State synced with local components
```

## Implementation Details

### Global Store Structure

```typescript
interface AgentConnection {
  agentId: string;
  conversationId: string;
  token: string | null;
  room: Room | null;
  connectionState: ConnectionState;
  messages: ChatMessage[];
  workflowStage: WorkflowStatus | null;
  steps: WorkflowStep[];
  isLoading: boolean;
  isMessageLoading: boolean;
  inputDisabled: boolean;
  lastActivity: number;
  error: string | null;
}
```

### Connection States

- `DISCONNECTED` - No active connection
- `CONNECTING` - Establishing connection
- `CONNECTED` - Active and ready
- `RECONNECTING` - Attempting to reconnect
- `FAILED` - Connection failed

### Key Methods

#### LiveKit Store
- `initializeConnection(agentId, conversationId)` - Create/retrieve connection
- `setActiveConnection(agentId, conversationId)` - Switch active connection
- `updateConnection(agentId, conversationId, updates)` - Update connection state
- `addMessage(agentId, conversationId, message)` - Add message to conversation
- `cleanupInactiveConnections()` - Remove old connections

#### LiveKit Manager
- `initializeConnection()` - Initialize connection with error handling
- `switchConnection()` - Switch between agents seamlessly
- `handleDisconnection()` - Handle unexpected disconnections
- `disconnectAll()` - Clean shutdown of all connections

## Usage

### Switching Between Agents

When a user navigates to a different agent:

1. `livekitManager.switchConnection(newAgentId, conversationId)` is called
2. If connection exists, it's activated immediately
3. If not, a new connection is initialized in the background
4. Local component state syncs with the global store
5. User sees immediate response with preserved state

### Message Persistence

Messages are stored in the global store and automatically restored when switching back to an agent:

```typescript
// Messages are automatically synced
useEffect(() => {
  if (connection) {
    setMessages(connection.messages);
    // ... other state restoration
  }
}, [connection]);
```

### Cleanup

Inactive connections are automatically cleaned up:
- Connections older than 30 minutes are removed
- Cleanup runs every 10 minutes
- Manual cleanup available via debug interface

## Debug Interface

In development mode, a debug panel shows:
- Number of active connections
- Connection states and message counts
- Manual cleanup and disconnect controls

## Benefits

1. **Seamless User Experience**: No reconnection delays when switching agents
2. **Preserved Context**: Conversation history and state maintained
3. **Resource Efficiency**: Automatic cleanup prevents memory leaks
4. **Scalability**: Can handle multiple concurrent agent connections
5. **Reliability**: Robust error handling and reconnection logic

## Configuration

### Environment Variables
- `NEXT_PUBLIC_LIVEKIT_URL` - LiveKit server URL
- `NODE_ENV` - Controls debug interface visibility

### Timeouts
- Inactive connection cleanup: 30 minutes
- Cleanup interval: 10 minutes
- Connection timeout: Configurable per connection

## Testing

The implementation includes:
- TypeScript compilation checks
- Build verification
- Debug interface for runtime testing
- Error boundary handling

## Future Enhancements

Potential improvements:
- Connection pooling optimization
- Metrics and monitoring
- Configurable cleanup intervals
- Connection health monitoring
- Offline state handling

## Migration Notes

This implementation maintains backward compatibility with existing chat functionality while adding persistent connection capabilities. No breaking changes to existing APIs.
